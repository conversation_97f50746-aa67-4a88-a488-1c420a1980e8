<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市场与竞品分析 - 兮禾文化商业计划书</title>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Condensed:wght@300;400;700&family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            overflow: hidden;
            background: #000;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #0a1a0a 0%, #0d1f0d 50%, #071307 100%);
            color: #ffffff;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        /* 电影噪点效果 */
        .noise {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iMC43NSIgbnVtT2N0YXZlcz0iMyIgc3RpdGNoVGlsZXM9InN0aXRjaCIvPjwvZmlsdGVyPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbHRlcj0idXJsKCNhKSIgb3BhY2l0eT0iMC4wNSIvPjwvc3ZnPg==');
            opacity: 0.4;
            z-index: 1;
            pointer-events: none;
        }
        /* 胶片颗粒效果 */
        .grain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iMC45IiBudW1PY3RhdmVzPSIxIi8+PC9maWx0ZXI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');
            opacity: 0.6;
            z-index: 1;
            pointer-events: none;
        }
        /* 光线效果 */
        .light-leak {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 255, 100, 0.1) 0%, rgba(0, 255, 100, 0) 70%);
            transform: rotate(45deg);
            z-index: 1;
            pointer-events: none;
        }
        .light-leak-2 {
            position: absolute;
            bottom: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 200, 80, 0.08) 0%, rgba(0, 200, 80, 0) 70%);
            transform: rotate(-30deg);
            z-index: 1;
            pointer-events: none;
        }
        /* 划痕效果 */
        .scratch {
            position: absolute;
            background: linear-gradient(90deg, rgba(0, 255, 100, 0), rgba(0, 255, 100, 0.1), rgba(0, 255, 100, 0));
            height: 1px;
            width: 100%;
            z-index: 1;
            pointer-events: none;
        }
        .scratch-1 {
            top: 20%;
            transform: rotate(0.5deg);
        }
        .scratch-2 {
            top: 65%;
            transform: rotate(-0.7deg);
        }
        .scratch-3 {
            top: 40%;
            transform: rotate(0.3deg);
            width: 70%;
            left: 15%;
        }
        .content {
            display: flex;
            flex-direction: column;
            padding: 60px 70px;
            flex-grow: 1;
            position: relative;
            z-index: 2;
        }
        .header {
            margin-bottom: 40px;
        }
        .title {
            font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif;
            font-size: 50px;
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: 2px;
            color: rgba(0, 255, 100, 0.9);
            text-transform: uppercase;
            position: relative;
            text-shadow: 0 0 20px rgba(0, 255, 100, 0.3);
        }
        .title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, rgba(0, 255, 100, 0.7), rgba(0, 255, 100, 0));
        }
        .main-content {
            display: flex;
            gap: 30px;
            flex-grow: 1;
        }
        .market-overview {
            flex: 1;
            background: rgba(10, 26, 10, 0.6);
            border: 1px solid rgba(0, 255, 100, 0.2);
            border-radius: 4px;
            padding: 25px;
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .market-overview:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 255, 100, 0.2);
        }
        .market-overview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 255, 100, 0.05), rgba(0, 200, 80, 0.02));
            z-index: -1;
        }
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .section-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
            position: relative;
        }
        .section-icon-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            filter: blur(10px);
            opacity: 0.7;
            background: radial-gradient(circle, rgba(0, 255, 100, 0.3) 0%, rgba(0, 255, 100, 0) 70%);
        }
        .section-icon-inner {
            width: 40px;
            height: 40px;
            background: rgba(10, 26, 10, 0.8);
            border: 2px solid rgba(0, 255, 100, 0.5);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(5px);
        }
        .section-icon i {
            font-size: 20px;
            color: rgba(0, 255, 100, 0.9);
        }
        .section-title {
            font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: 1px;
            color: rgba(0, 255, 100, 0.9);
            text-transform: uppercase;
        }
        .chart-container {
            flex-grow: 1;
            position: relative;
            height: 220px;
            margin-bottom: 20px;
        }
        .key-stats {
            display: flex;
            justify-content: space-around;
        }
        .stat-item {
            text-align: center;
            position: relative;
            padding: 15px;
            border-radius: 4px;
            background: rgba(10, 26, 10, 0.4);
            border: 1px solid rgba(0, 255, 100, 0.1);
            transition: transform 0.3s ease;
        }
        .stat-item:hover {
            transform: translateY(-5px);
        }
        .stat-value {
            font-family: 'Bebas Neue', sans-serif;
            font-size: 36px;
            font-weight: 700;
            color: rgba(0, 255, 100, 0.9);
            text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
        }
        .track-selection {
            flex: 1;
            background: rgba(10, 26, 10, 0.6);
            border: 1px solid rgba(0, 255, 100, 0.2);
            border-radius: 4px;
            padding: 25px;
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .track-selection:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 255, 100, 0.2);
        }
        .track-selection::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 255, 100, 0.05), rgba(0, 200, 80, 0.02));
            z-index: -1;
        }
        .track-comparison {
            display: flex;
            flex-direction: column;
            gap: 15px;
            flex-grow: 1;
        }
        .track {
            display: flex;
            align-items: center;
            background: rgba(10, 26, 10, 0.4);
            border: 1px solid rgba(0, 255, 100, 0.1);
            border-radius: 4px;
            padding: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .track:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 255, 100, 0.2);
        }
        .track::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(0, 255, 100, 0.05), rgba(0, 255, 100, 0));
            z-index: -1;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .track:hover::before {
            opacity: 1;
        }
        .track-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
            position: relative;
        }
        .track-icon-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            filter: blur(8px);
            opacity: 0.7;
        }
        .red-ocean-icon-bg {
            background: radial-gradient(circle, rgba(255, 50, 50, 0.3) 0%, rgba(255, 50, 50, 0) 70%);
        }
        .blue-ocean-icon-bg {
            background: radial-gradient(circle, rgba(0, 255, 100, 0.3) 0%, rgba(0, 255, 100, 0) 70%);
        }
        .track-icon-inner {
            width: 32px;
            height: 32px;
            background: rgba(10, 26, 10, 0.8);
            border: 2px solid rgba(0, 255, 100, 0.5);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(5px);
        }
        .red-ocean-icon-inner {
            border-color: rgba(255, 50, 50, 0.7);
        }
        .blue-ocean-icon-inner {
            border-color: rgba(0, 255, 100, 0.7);
        }
        .track-icon i {
            font-size: 16px;
        }
        .red-ocean-icon i {
            color: rgba(255, 50, 50, 0.9);
        }
        .blue-ocean-icon i {
            color: rgba(0, 255, 100, 0.9);
        }
        .track-details {
            flex-grow: 1;
        }
        .track-name {
            font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 5px;
            letter-spacing: 1px;
        }
        .red-ocean-name {
            color: rgba(255, 50, 50, 0.9);
            text-shadow: 0 0 15px rgba(255, 50, 50, 0.3);
        }
        .blue-ocean-name {
            color: rgba(0, 255, 100, 0.9);
            text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);
        }
        .track-desc {
            font-size: 16px;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.7);
        }
        .track-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        .feature-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        .red-ocean-tag {
            background: rgba(255, 50, 50, 0.1);
            color: rgba(255, 50, 50, 0.9);
            border: 1px solid rgba(255, 50, 50, 0.3);
        }
        .blue-ocean-tag {
            background: rgba(0, 255, 100, 0.1);
            color: rgba(0, 255, 100, 0.9);
            border: 1px solid rgba(0, 255, 100, 0.3);
        }
        .frame {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 1px solid rgba(0, 255, 100, 0.2);
            z-index: 1;
            pointer-events: none;
        }
        .frame::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border: 1px solid rgba(0, 255, 100, 0.1);
            transform: translate(5px, 5px);
        }
        .corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border-color: rgba(0, 255, 100, 0.5);
            border-style: solid;
            border-width: 0;
            z-index: 2;
        }
        .corner-tl {
            top: 20px;
            left: 20px;
            border-top-width: 2px;
            border-left-width: 2px;
        }
        .corner-tr {
            top: 20px;
            right: 20px;
            border-top-width: 2px;
            border-right-width: 2px;
        }
        .corner-bl {
            bottom: 20px;
            left: 20px;
            border-bottom-width: 2px;
            border-left-width: 2px;
        }
        .corner-br {
            bottom: 20px;
            right: 20px;
            border-bottom-width: 2px;
            border-right-width: 2px;
        }
        .footer {
            padding: 20px 70px;
            text-align: center;
            font-size: 14px;
            color: rgba(0, 255, 100, 0.5);
            position: relative;
            z-index: 2;
            font-family: 'Roboto Condensed', sans-serif;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="noise"></div>
        <div class="grain"></div>
        <div class="light-leak"></div>
        <div class="light-leak-2"></div>
        <div class="scratch scratch-1"></div>
        <div class="scratch scratch-2"></div>
        <div class="scratch scratch-3"></div>
        <div class="frame"></div>
        <div class="corner corner-tl"></div>
        <div class="corner corner-tr"></div>
        <div class="corner corner-bl"></div>
        <div class="corner corner-br"></div>
        
        <div class="content">
            <div class="header">
                <h1 class="title">市场与竞品分析</h1>
            </div>
            
            <div class="main-content">
                <div class="market-overview">
                    <div class="section-header">
                        <div class="section-icon">
                            <div class="section-icon-bg"></div>
                            <div class="section-icon-inner">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                        </div>
                        <h2 class="section-title">市场概览</h2>
                    </div>
                    <div class="chart-container">
                        <canvas id="marketChart"></canvas>
                    </div>
                    <div class="key-stats">
                        <div class="stat-item">
                            <div class="stat-value">743亿</div>
                            <div class="stat-label">市场规模</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">35%+</div>
                            <div class="stat-label">年增速</div>
                        </div>
                    </div>
                </div>
                
                <div class="track-selection">
                    <div class="section-header">
                        <div class="section-icon">
                            <div class="section-icon-bg"></div>
                            <div class="section-icon-inner">
                                <i class="fas fa-route"></i>
                            </div>
                        </div>
                        <h2 class="section-title">赛道选择</h2>
                    </div>
                    <div class="track-comparison">
                        <div class="track">
                            <div class="track-icon">
                                <div class="track-icon-bg red-ocean-icon-bg"></div>
                                <div class="track-icon-inner red-ocean-icon-inner">
                                    <i class="fas fa-water"></i>
                                </div>
                            </div>
                            <div class="track-details">
                                <div class="track-name red-ocean-name">红海赛道</div>
                                <div class="track-desc">传统内容营销与MCN机构，竞争激烈，同质化严重</div>
                                <div class="track-features">
                                    <span class="feature-tag red-ocean-tag">流量竞争</span>
                                    <span class="feature-tag red-ocean-tag">价格战</span>
                                    <span class="feature-tag red-ocean-tag">低毛利</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="track">
                            <div class="track-icon">
                                <div class="track-icon-bg blue-ocean-icon-bg"></div>
                                <div class="track-icon-inner blue-ocean-icon-inner">
                                    <i class="fas fa-compass"></i>
                                </div>
                            </div>
                            <div class="track-details">
                                <div class="track-name blue-ocean-name">蓝海赛道</div>
                                <div class="track-desc">AI驱动的文化IP孵化与商业化，差异化明显，高增长潜力</div>
                                <div class="track-features">
                                    <span class="feature-tag blue-ocean-tag">技术壁垒</span>
                                    <span class="feature-tag blue-ocean-tag">文化价值</span>
                                    <span class="feature-tag blue-ocean-tag">高附加值</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            兮禾文化 · 创意与科技的融合
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('marketChart').getContext('2d');
            const marketChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['文化IP孵化', '内容营销', '数字资产管理', '其他'],
                    datasets: [{
                        data: [35, 25, 30, 10],
                        backgroundColor: [
                            'rgba(0, 255, 100, 0.7)',
                            'rgba(0, 200, 80, 0.7)',
                            'rgba(0, 150, 60, 0.7)',
                            'rgba(0, 100, 40, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 255, 100, 1)',
                            'rgba(0, 200, 80, 1)',
                            'rgba(0, 150, 60, 1)',
                            'rgba(0, 100, 40, 1)'
                        ],
                        borderWidth: 1,
                        hoverOffset: 15
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                font: {
                                    family: "'Noto Sans SC', sans-serif",
                                    size: 14
                                },
                                padding: 15
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(10, 26, 10, 0.8)',
                            titleColor: '#fff',
                            bodyColor: 'rgba(255, 255, 255, 0.8)',
                            titleFont: {
                                size: 14,
                                family: "'Noto Sans SC', sans-serif"
                            },
                            bodyFont: {
                                size: 14,
                                family: "'Noto Sans SC', sans-serif"
                            },
                            padding: 10,
                            cornerRadius: 4,
                            displayColors: true
                        }
                    },
                    cutout: '60%'
                }
            });
        });
    </script>
</body>
</html>