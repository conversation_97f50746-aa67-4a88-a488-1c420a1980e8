<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>商业模式架构 - 兮禾文化</title>
<link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Condensed:wght@300;400;700&family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
<style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
        font-family: 'Noto Sans SC', sans-serif;
        overflow: hidden;
        background: #000;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100vw;
    }

    .slide {
        position: relative;
        width: 100%;
        height: 100%;
        max-width: 1280px;
        max-height: 720px;
        aspect-ratio: 16/9;
        background: #0d0d0d;
        color: #ffffff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 40px;
    }
    
    /* 复用视觉特效 */
    .texture, .grain, .light-leak {
        position: absolute; z-index: 1; pointer-events: none; width: 100%; height: 100%; top: 0; left: 0;
    }
    .texture {
        background-image: linear-gradient(rgba(0, 255, 0, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 255, 0, 0.03) 1px, transparent 1px);
        background-size: 40px 40px;
    }
    .grain {
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iMC45IiBudW1PY3RhdmVzPSIxIi8+PC9maWx0ZXI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIwLjA1Ii8+PC9zdmc+');
        opacity: 0.8;
    }
    .light-leak {
        width: 200%; height: 200%;
        background: radial-gradient(circle, rgba(0, 255, 0, 0.08) 0%, rgba(0, 255, 0, 0) 60%);
        top: 50%; left: 50%; transform: translate(-50%, -50%);
    }

    .content {
        position: relative;
        z-index: 2;
        width: 100%;
        height: 100%;
        display: grid;
        grid-template-columns: 1fr 1.5fr 1fr;
        grid-template-rows: 1fr 1.5fr 1fr;
        gap: 20px;
        align-items: center;
        justify-items: center;
    }

    .box {
        background: rgba(10, 26, 10, 0.7);
        border: 1px solid rgba(0, 255, 0, 0.2);
        border-radius: 4px;
        padding: 15px;
        text-align: center;
        backdrop-filter: blur(5px);
        width: 100%;
        max-width: 250px;
    }
    .box h3 {
        font-family: 'Roboto Condensed', sans-serif;
        font-size: 18px;
        color: #00ff00;
        margin-bottom: 5px;
        text-transform: uppercase;
    }
    .box p { font-size: 12px; color: #ccc; line-height: 1.5; }

    .central-hub {
        grid-column: 2;
        grid-row: 2;
        width: 280px;
        height: 180px;
        border: 2px solid #00ff00;
        box-shadow: 0 0 20px rgba(0, 255, 0, 0.4);
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .central-hub h3 { font-size: 24px; }

    .input-source { grid-column: 1; grid-row: 2; }
    .c-loop-platform { grid-column: 2; grid-row: 1; }
    .c-loop-user { grid-column: 3; grid-row: 1; }
    .b-loop-client { grid-column: 3; grid-row: 2; }
    .saas-loop { grid-column: 3; grid-row: 3; }
    .ad-loop { grid-column: 2; grid-row: 3; }

    /* Arrows */
    .arrow {
        position: absolute;
        stroke: rgba(0, 255, 0, 0.6);
        stroke-width: 1.5;
        fill: none;
        stroke-dasharray: 4;
        animation: dash 2s linear infinite;
    }
    @keyframes dash { to { stroke-dashoffset: -20; } }
    .arrow-label {
        position: absolute;
        font-size: 11px;
        color: #00ff00;
        background: #0d0d0d;
        padding: 0 4px;
        transform: translate(-50%, -50%);
    }

</style>
</head>
<body>
    <div class="slide">
        <div class="texture"></div>
        <div class="grain"></div>
        <div class="light-leak"></div>
        
        <div class="content">
            <!-- Entities -->
            <div class="box input-source">
                <h3>外部智库</h3>
                <p>学术/艺术机构/专家</p>
            </div>
            <div class="box central-hub">
                <h3>兮禾文化</h3>
                <p>价值创造引擎 (Agent Inc.)</p>
            </div>
            <div class="box c-loop-platform">
                <h3>To C 内容矩阵</h3>
                <p>抖音/B站/小红书等</p>
            </div>
            <div class="box c-loop-user">
                <h3>高知价值用户</h3>
                <p>品牌认知/粉丝沉淀</p>
            </div>
            <div class="box b-loop-client">
                <h3>B端品牌客户</h3>
                <p>IP共创/战略咨询</p>
            </div>
             <div class="box saas-loop">
                <h3>文化DAM系统</h3>
                <p>SaaS订阅/长期绑定</p>
            </div>
            <div class="box ad-loop">
                <h3>广告主</h3>
                <p>推广内容制作/广告费</p>
            </div>
        </div>

        <!-- SVG Arrows and Labels -->
        <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 3;">
            <defs>
                <marker id="arrowhead" markerWidth="6" markerHeight="4" refX="0" refY="2" orient="auto">
                    <polygon points="0 0, 6 2, 0 4" fill="#00ff00" />
                </marker>
            </defs>
            
            <!-- Input to Hub -->
            <path class="arrow" d="M 200,360 H 380" marker-end="url(#arrowhead)"></path>
            <!-- Hub to C Platform -->
            <path class="arrow" d="M 520,300 V 200" marker-end="url(#arrowhead)"></path>
            <!-- C Platform to C User -->
            <path class="arrow" d="M 640,160 H 880" marker-end="url(#arrowhead)"></path>
            <!-- C User to Hub -->
            <path class="arrow" d="M 940,200 C 800,220 700,280 680,330" marker-end="url(#arrowhead)"></path>
            <!-- Hub to B Client -->
            <path class="arrow" d="M 680,360 H 880" marker-end="url(#arrowhead)"></path>
            <!-- B Client to Hub -->
            <path class="arrow" d="M 880,400 H 680" marker-end="url(#arrowhead)"></path>
            <!-- Hub to SaaS -->
            <path class="arrow" d="M 680,430 C 720,480 800,520 880,530" marker-end="url(#arrowhead)"></path>
             <!-- Hub to Ad -->
            <path class="arrow" d="M 520,460 V 530" marker-end="url(#arrowhead)"></path>
        </svg>

        <div class="arrow-label" style="top: 360px; left: 290px;">思想注入</div>
        <div class="arrow-label" style="top: 250px; left: 535px;">IP内容发布</div>
        <div class="arrow-label" style="top: 160px; left: 760px;">深度内容</div>
        <div class="arrow-label" style="top: 265px; left: 810px;">品牌认知/B端线索</div>
        <div class="arrow-label" style="top: 350px; left: 780px;">IP共创/DAM系统</div>
        <div class="arrow-label" style="top: 410px; left: 780px;">服务费/订阅费</div>
        <div class="arrow-label" style="top: 500px; left: 780px;">SaaS产品</div>
        <div class="arrow-label" style="top: 500px; left: 535px;">推广内容</div>
    </div>
</body>
</html>