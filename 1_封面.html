<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兮禾文化商业计划书</title>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Condensed:wght@300;400;700&family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            overflow: hidden;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            width: 100vw;
        }
        
        /* 自适应容器 - 保持正方形1:1比例 */
        .slide-container {
            position: relative;
            width: 100%;
            height: 100%;
            max-width: 720px;
            max-height: 720px;
            aspect-ratio: 1/1;
            background: linear-gradient(135deg, #1a1a1a 0%, #222222 50%, #1d1d1d 100%);
            color: #ffffff;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        /* 胶片边框效果 - 增强电影质感 */
        .film-border {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 20px solid transparent;
            border-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><rect width="40" height="40" fill="black"/><rect x="5" y="5" width="30" height="30" fill="none" stroke="%23228B22" stroke-width="1"/><circle cx="10" cy="10" r="2" fill="%23228B22"/><circle cx="30" cy="10" r="2" fill="%23228B22"/><circle cx="10" cy="30" r="2" fill="%23228B22"/><circle cx="30" cy="30" r="2" fill="%23228B22"/></svg>') 20 repeat;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 背景纹理 - 增强质感 */
        .texture {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(34, 139, 34, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(34, 139, 34, 0.05) 1px, transparent 1px);
            background-size: 30px 30px;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 增强的胶片颗粒效果 */
        .grain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iMC45IiBudW1PY3RhdmVzPSIxIi8+PC9maWx0ZXI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIwLjIiLz48L3N2Zz4=');
            opacity: 0.6;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 增强的光线泄露效果 */
        .light-leak {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(34, 139, 34, 0.2) 0%, rgba(34, 139, 34, 0) 70%);
            transform: rotate(45deg);
            z-index: 1;
            pointer-events: none;
            animation: light-leak-move 20s infinite alternate;
        }
        
        @keyframes light-leak-move {
            0% { transform: rotate(45deg) translate(-10%, -10%); }
            100% { transform: rotate(45deg) translate(10%, 10%); }
        }
        
        .light-leak-2 {
            position: absolute;
            bottom: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 200, 0, 0.15) 0%, rgba(0, 200, 0, 0) 70%);
            transform: rotate(-30deg);
            z-index: 1;
            pointer-events: none;
            animation: light-leak-move-2 25s infinite alternate;
        }
        
        @keyframes light-leak-move-2 {
            0% { transform: rotate(-30deg) translate(10%, -10%); }
            100% { transform: rotate(-30deg) translate(-10%, 10%); }
        }
        
        /* 增强的划痕效果 */
        .scratch {
            position: absolute;
            background: linear-gradient(45deg, rgba(0, 255, 0, 0), rgba(0, 255, 0, 0.4), rgba(0, 255, 0, 0));
            height: 1px;
            width: 100%;
            z-index: 1;
            pointer-events: none;
            animation: scratch-flicker 8s infinite;
        }
        .scratch-1 {
            top: 20%;
            transform: rotate(0.5deg);
            animation-delay: 0s;
        }
        .scratch-2 {
            top: 65%;
            transform: rotate(-0.7deg);
            animation-delay: 2s;
        }
        .scratch-3 {
            top: 40%;
            transform: rotate(0.3deg);
            width: 70%;
            left: 15%;
            animation-delay: 4s;
        }
        .scratch-4 {
            top: 30%;
            transform: rotate(-0.2deg);
            width: 60%;
            right: 10%;
            animation-delay: 6s;
        }
        
        .scratch-5 {
            top: 8%;
            left: -10%;
            width: 40%;
            transform: rotate(25deg);
            animation-delay: 1s;
        }
        
        .scratch-6 {
            top: 85%;
            right: -5%;
            width: 35%;
            transform: rotate(-15deg);
            animation-delay: 5s;
        }
        
        @keyframes scratch-flicker {
            0%, 90%, 100% { opacity: 0; }
            91%, 95% { opacity: 0.6; }
        }
        
        /* 精密网格背景 */
        .precision-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 0, 0.15) 0.5px, transparent 0.5px),
                linear-gradient(90deg, rgba(0, 255, 0, 0.15) 0.5px, transparent 0.5px);
            background-size: 50px 50px;
            z-index: 1;
            pointer-events: none;
            animation: grid-dance 10s linear infinite;
        }
        
        @keyframes grid-dance {
            0% { background-position: 0 0; }
            100% { background-position: 50px 50px; }
        }
        
        /* 数字雨效果 */
        .digital-rain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
            pointer-events: none;
        }
        
        .rain-drop {
            position: absolute;
            color: rgba(0, 255, 0, 0.3);
            font-family: 'Courier New', monospace;
            font-size: 10px;
            animation: rain-fall linear infinite;
            user-select: none;
        }
        
        @keyframes rain-fall {
            0% { transform: translateY(-100px); opacity: 1; }
            100% { transform: translateY(calc(100vh + 100px)); opacity: 0; }
        }
        
        .content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px;
            flex-grow: 1;
            position: relative;
            z-index: 2;
            width: calc(100% - 40px);
            height: calc(100% - 40px);
            margin: 20px;
        }
        
        .logo-container {
            margin-bottom: 60px;
            position: relative;
        }
        
        .logo {
            width: 140px;
            height: 140px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .logo-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            background: conic-gradient(from 0deg, rgba(0, 255, 0, 0.2), rgba(0, 204, 0, 0.4), rgba(0, 255, 0, 0.6), rgba(0, 204, 0, 0.4), rgba(0, 255, 0, 0.2));
            border-radius: 50%;
            filter: blur(15px);
            animation: rotate-gradient 6s linear infinite;
            box-shadow: 0 0 50px rgba(0, 255, 0, 0.3);
        }
        
        @keyframes rotate-gradient {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        @keyframes pulse {
            0% { transform: scale(0.95) rotate(0deg); opacity: 0.7; }
            100% { transform: scale(1.05) rotate(360deg); opacity: 0.9; }
        }
        
        .logo-inner {
            width: 110px;
            height: 110px;
            background: linear-gradient(145deg, rgba(15, 15, 15, 0.9), rgba(40, 40, 40, 0.9));
            border: 3px solid transparent;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(8px);
            box-shadow:
                0 0 30px rgba(0, 255, 0, 0.6),
                inset 0 0 20px rgba(0, 255, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .logo-inner::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 255, 0, 0.8), transparent);
            animation: border-rotate 2s linear infinite;
            z-index: -1;
        }
        
        @keyframes border-rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .logo-inner:hover {
            transform: scale(1.1) rotate(15deg);
            box-shadow:
                0 0 50px rgba(0, 255, 0, 0.8),
                inset 0 0 30px rgba(0, 255, 0, 0.2);
        }
        
        .logo-inner i {
            font-size: 45px;
            color: #00ff00;
            text-shadow: 0 0 15px rgba(0, 255, 0, 0.9);
            animation: icon-breathe 2s ease-in-out infinite alternate;
        }
        
        @keyframes icon-breathe {
            0% { transform: scale(1); filter: brightness(1); }
            100% { transform: scale(1.1); filter: brightness(1.3); }
        }
        
        .title-container {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            width: 100%;
            max-width: 800px;
        }
        
        .title {
            font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif;
            font-size: clamp(48px, 7vw, 90px);
            font-weight: 900;
            margin-bottom: 15px;
            letter-spacing: 8px;
            color: #00ff00;
            text-transform: uppercase;
            position: relative;
            text-shadow: 0 0 30px rgba(0, 255, 0, 0.8), 0 0 60px rgba(0, 255, 0, 0.4);
            transform: perspective(1000px) rotateX(15deg) skew(-5deg);
            white-space: nowrap;
            animation: title-glow 3s ease-in-out infinite alternate;
            background: linear-gradient(45deg, #00ff00, #00cc00, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-size: 200% auto;
            animation: title-gradient 4s ease-in-out infinite;
        }
        
        @keyframes title-glow {
            0% { filter: brightness(1) drop-shadow(0 0 20px rgba(0, 255, 0, 0.5)); }
            100% { filter: brightness(1.3) drop-shadow(0 0 40px rgba(0, 255, 0, 0.8)); }
        }
        
        @keyframes title-gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .subtitle {
            font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif;
            font-size: clamp(24px, 3vw, 36px);
            font-weight: 200;
            color: #00cc00;
            letter-spacing: 12px;
            text-transform: uppercase;
            margin-top: 25px;
            text-shadow: 0 0 25px rgba(0, 204, 0, 0.7);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }
        
        .subtitle::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 204, 0, 0.8), transparent);
            animation: scan-line 3s infinite;
        }
        
        @keyframes scan-line {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .decorative-line {
            width: 300px;
            height: 4px;
            background: linear-gradient(90deg,
                transparent,
                rgba(0, 255, 0, 0.8) 20%,
                rgba(0, 255, 0, 0.2) 50%,
                rgba(0, 255, 0, 0.8) 80%,
                transparent);
            margin: 40px auto;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.6);
            position: relative;
            overflow: hidden;
        }
        
        .decorative-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            animation: scan-light 3s infinite;
        }
        
        @keyframes scan-light {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .tagline {
            font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif;
            font-size: clamp(18px, 2.2vw, 28px);
            font-weight: 300;
            color: #ffffff;
            text-align: center;
            max-width: 700px;
            line-height: 1.8;
            margin-top: 35px;
            letter-spacing: 3px;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
            position: relative;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(0, 255, 0, 0.8), rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-size: 200% auto;
            animation: text-shimmer 4s ease-in-out infinite;
        }
        
        @keyframes text-shimmer {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 图标容器 */
        .icons-container {
            display: flex;
            justify-content: center;
            gap: 50px;
            margin-top: 50px;
            width: 100%;
            max-width: 700px;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            flex: 1;
            cursor: pointer;
        }
        
        .icon-item:hover {
            transform: translateY(-15px) scale(1.1);
        }
        
        .icon-circle {
            width: 80px;
            height: 80px;
            background: linear-gradient(145deg, rgba(20, 20, 20, 0.9), rgba(30, 30, 30, 0.9));
            border: 2px solid rgba(0, 255, 0, 0.4);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            box-shadow:
                0 0 25px rgba(0, 255, 0, 0.3),
                inset 0 0 20px rgba(0, 255, 0, 0.1);
            backdrop-filter: blur(8px);
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .icon-circle::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 255, 0, 0.6), transparent);
            animation: icon-rotate 5s linear infinite;
            opacity: 0.7;
        }
        
        @keyframes icon-rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .icon-circle:hover {
            border-color: rgba(0, 255, 0, 1);
            box-shadow:
                0 0 40px rgba(0, 255, 0, 0.7),
                inset 0 0 30px rgba(0, 255, 0, 0.3);
        }
        
        .icon-circle i {
            font-size: 35px;
            color: #00ff00;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.9);
            z-index: 2;
            position: relative;
            transition: transform 0.3s ease;
        }
        
        .icon-circle:hover i {
            transform: scale(1.2) rotate(15deg);
        }
        
        .icon-label {
            font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif;
            font-size: clamp(16px, 1.4vw, 20px);
            font-weight: 500;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-align: center;
            margin-top: 5px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .icon-item:hover .icon-label {
            color: #00ff00;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
            letter-spacing: 3px;
        }
        
        .icon-label::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00ff00, transparent);
            transition: width 0.3s ease;
            transform: translateX(-50%);
        }
        
        .icon-item:hover .icon-label::after {
            width: 100%;
        }
        
        /* 先锋艺术感装饰元素 */
        .art-decoration {
            position: absolute;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 几何形状装饰 */
        .geometric-shape {
            position: absolute;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .shape-1 {
            width: 120px;
            height: 120px;
            top: 10%;
            left: 5%;
            transform: rotate(45deg);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        }
        
        .shape-2 {
            width: 80px;
            height: 80px;
            bottom: 15%;
            right: 8%;
            transform: rotate(30deg);
            clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
        }
        
        /* 不对称线条装饰 */
        .asymmetric-line {
            position: absolute;
            background: linear-gradient(90deg, rgba(0, 255, 0, 0), rgba(0, 255, 0, 0.5), rgba(0, 255, 0, 0));
            height: 2px;
            transform-origin: left center;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        .line-1 {
            width: 200px;
            top: 25%;
            left: 0;
            transform: rotate(15deg);
        }
        
        .line-2 {
            width: 150px;
            bottom: 20%;
            right: 0;
            transform: rotate(-20deg);
        }
        
        /* 电影胶片装饰 */
        .film-strip {
            position: absolute;
            width: 100%;
            height: 40px;
            background: repeating-linear-gradient(
                90deg,
                rgba(0, 0, 0, 0.8),
                rgba(0, 0, 0, 0.8) 10px,
                rgba(34, 139, 34, 0.3) 10px,
                rgba(34, 139, 34, 0.3) 20px
            );
            z-index: 1;
        }
        
        .film-strip-top {
            top: 0;
            left: 0;
        }
        
        .film-strip-bottom {
            bottom: 0;
            left: 0;
        }
        
        .film-hole {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(34, 139, 34, 0.5);
        }
        
        /* 动态光效 */
        .light-beam {
            position: absolute;
            width: 2px;
            height: 100px;
            background: linear-gradient(to bottom, rgba(0, 255, 0, 0), rgba(0, 255, 0, 0.8), rgba(0, 255, 0, 0));
            transform-origin: top center;
            animation: beam-sweep 8s infinite alternate;
            z-index: 1;
        }
        
        @keyframes beam-sweep {
            0% { transform: rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: rotate(30deg); opacity: 0; }
        }
        
        .beam-1 {
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }
        
        .beam-2 {
            top: 60%;
            right: 20%;
            animation-delay: 4s;
        }
        
        .footer {
            padding: 20px 70px;
            text-align: center;
            font-size: clamp(14px, 1.41vw, 18px);
            color: #00ff00;
            position: relative;
            z-index: 2;
            font-family: 'Roboto Condensed', sans-serif;
            letter-spacing: 3px;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        /* 响应式调整 - 保持1280x720比例 */
        @media (max-aspect-ratio: 1280/720) {
            .slide-container {
                width: 100vw;
                height: calc(100vw * 720 / 1280);
            }
            
            /* 在小屏幕上调整元素大小 */
            .logo {
                width: 80px;
                height: 80px;
            }
            
            .logo-inner {
                width: 70px;
                height: 70px;
            }
            
            .logo-inner i {
                font-size: 30px;
            }
            
            .icon-circle {
                width: 50px;
                height: 50px;
            }
            
            .icon-circle i {
                font-size: 20px;
            }
            
            .icons-container {
                gap: 20px;
            }
        }
        
        @media (min-aspect-ratio: 1280/720) {
            .slide-container {
                width: calc(100vh * 1280 / 720);
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 背景纹理 -->
        <div class="texture"></div>
        
        <!-- 电影质感元素 -->
        <div class="film-border"></div>
        <div class="film-strip film-strip-top"></div>
        <div class="film-strip film-strip-bottom"></div>
        
        <!-- 胶片孔 - 顶部 -->
        <div class="film-hole" style="top: 16px; left: 30px;"></div>
        <div class="film-hole" style="top: 16px; left: 70px;"></div>
        <div class="film-hole" style="top: 16px; left: 110px;"></div>
        <div class="film-hole" style="top: 16px; left: 150px;"></div>
        <div class="film-hole" style="top: 16px; left: 190px;"></div>
        <div class="film-hole" style="top: 16px; left: 230px;"></div>
        <div class="film-hole" style="top: 16px; left: 270px;"></div>
        <div class="film-hole" style="top: 16px; left: 310px;"></div>
        <div class="film-hole" style="top: 16px; left: 350px;"></div>
        <div class="film-hole" style="top: 16px; left: 390px;"></div>
        <div class="film-hole" style="top: 16px; left: 430px;"></div>
        <div class="film-hole" style="top: 16px; left: 470px;"></div>
        <div class="film-hole" style="top: 16px; left: 510px;"></div>
        <div class="film-hole" style="top: 16px; left: 550px;"></div>
        <div class="film-hole" style="top: 16px; left: 590px;"></div>
        <div class="film-hole" style="top: 16px; left: 630px;"></div>
        <div class="film-hole" style="top: 16px; left: 670px;"></div>
        <div class="film-hole" style="top: 16px; left: 710px;"></div>
        <div class="film-hole" style="top: 16px; left: 750px;"></div>
        <div class="film-hole" style="top: 16px; left: 790px;"></div>
        <div class="film-hole" style="top: 16px; left: 830px;"></div>
        <div class="film-hole" style="top: 16px; left: 870px;"></div>
        <div class="film-hole" style="top: 16px; left: 910px;"></div>
        <div class="film-hole" style="top: 16px; left: 950px;"></div>
        <div class="film-hole" style="top: 16px; left: 990px;"></div>
        <div class="film-hole" style="top: 16px; left: 1030px;"></div>
        <div class="film-hole" style="top: 16px; left: 1070px;"></div>
        <div class="film-hole" style="top: 16px; left: 1110px;"></div>
        <div class="film-hole" style="top: 16px; left: 1150px;"></div>
        <div class="film-hole" style="top: 16px; left: 1190px;"></div>
        <div class="film-hole" style="top: 16px; left: 1230px;"></div>
        
        <!-- 胶片孔 - 底部 -->
        <div class="film-hole" style="bottom: 16px; left: 30px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 70px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 110px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 150px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 190px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 230px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 270px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 310px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 350px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 390px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 430px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 470px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 510px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 550px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 590px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 630px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 670px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 710px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 750px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 790px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 830px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 870px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 910px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 950px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 990px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 1030px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 1070px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 1110px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 1150px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 1190px;"></div>
        <div class="film-hole" style="bottom: 16px; left: 1230px;"></div>
        
       <!-- 增强背景元素 -->
       <div class="precision-grid"></div>
       <div class="digital-rain" id="digital-rain"></div>
       
       <div class="grain"></div>
       <div class="light-leak"></div>
       <div class="light-leak-2"></div>
       <div class="scratch scratch-1"></div>
       <div class="scratch scratch-2"></div>
       <div class="scratch scratch-3"></div>
       <div class="scratch scratch-4"></div>
       <div class="scratch scratch-5"></div>
       <div class="scratch scratch-6"></div>
        
        <!-- 先锋艺术感装饰元素 -->
        <div class="art-decoration geometric-shape shape-1"></div>
        <div class="art-decoration geometric-shape shape-2"></div>
        <div class="art-decoration asymmetric-line line-1"></div>
        <div class="art-decoration asymmetric-line line-2"></div>
        <div class="art-decoration light-beam beam-1"></div>
        <div class="art-decoration light-beam beam-2"></div>
        
        <div class="content">
            <div class="logo-container">
                <div class="logo-bg"></div>
                <div class="logo">
                    <div class="logo-inner">
                        <i class="fas fa-leaf"></i>
                    </div>
                </div>
            </div>
            
            <div class="title-container">
                <h1 class="title">兮禾文化</h1>
                <h2 class="subtitle">商业计划书</h2>
            </div>
            
            <div class="decorative-line"></div>
            
            <p class="tagline">AI驱动的文化价值重构者</p>
            
            <!-- 图标区域 -->
            <div class="icons-container">
                <div class="icon-item" data-label="INTELLIGENT">
                    <div class="icon-circle">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="icon-label">智能</div>
                </div>
                <div class="icon-item" data-label="CREATIVE">
                    <div class="icon-circle">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="icon-label">创意</div>
                </div>
                <div class="icon-item" data-label="CULTURAL">
                    <div class="icon-circle">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="icon-label">文化</div>
                </div>
                <div class="icon-item" data-label="GROWTH">
                    <div class="icon-circle">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="icon-label">增长</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            2024
        </div>
    </div>
    
    <script>
        // 数字雨效果
        function createDigitalRain() {
            const container = document.getElementById('digital-rain');
            const chars = '01+-*/%$#';
            
            setInterval(() => {
                if (Math.random() > 0.7) {
                    const drop = document.createElement('div');
                    drop.className = 'rain-drop';
                    drop.textContent = chars.charAt(Math.floor(Math.random() * chars.length));
                    
                    const x = Math.random() * 100;
                    const duration = 2 + Math.random() * 4;
                    
                    drop.style.left = x + '%';
                    drop.style.animationDuration = duration + 's';
                    
                    container.appendChild(drop);
                    
                    setTimeout(() => {
                        drop.remove();
                    }, duration * 1000 + 1000);
                }
            }, 200);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', createDigitalRain);
        
        // 鼠标交互效果
        document.addEventListener('mousemove', (e) => {
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            // 轻微信号光效跟随
            const lightBeams = document.querySelectorAll('.light-beam');
            lightBeams.forEach(beam => {
                const originalRotation = beam.classList.contains('beam-1') ? 0 : 30;
                const mouseInfluence = (mouseX - 0.5) * 20;
                beam.style.animationDelay = mouseInfluence * -0.1 + 's';
            });
        });
    </script>
        </div>
        
        <div class="footer">
            2024
        </div>
    </div>
</body>
</html>