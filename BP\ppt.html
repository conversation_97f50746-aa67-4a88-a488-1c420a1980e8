<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兮禾文化商业计划书</title>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Condensed:wght@300;400;700&family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            overflow: hidden;
            background: #000;
            position: relative;
        }
        
        /* 幻灯片容器 */
        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 自适应容器 - 保持原始1280x720比例 */
        .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            max-width: 1280px;
            max-height: 720px;
            aspect-ratio: 1280/720;
            background: linear-gradient(135deg, #1a1a1a 0%, #222222 50%, #1d1d1d 100%);
            color: #ffffff;
            overflow: hidden;
            display: none;
            flex-direction: column;
        }
        
        .slide.active {
            display: flex;
        }
        
        /* 胶片边框效果 */
        .film-border {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 20px solid transparent;
            border-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><rect width="40" height="40" fill="black"/><rect x="5" y="5" width="30" height="30" fill="none" stroke="%2300ff00" stroke-width="1"/><circle cx="10" cy="10" r="2" fill="%2300ff00"/><circle cx="30" cy="10" r="2" fill="%2300ff00"/><circle cx="10" cy="30" r="2" fill="%2300ff00"/><circle cx="30" cy="30" r="2" fill="%2300ff00"/></svg>') 20 repeat;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 背景纹理 */
        .texture {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 0, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 0, 0.05) 1px, transparent 1px);
            background-size: 30px 30px;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 胶片颗粒效果 */
        .grain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iMC45IiBudW1PY3RhdmVzPSIxIi8+PC9maWx0ZXI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIwLjIiLz48L3N2Zz4=');
            opacity: 0.6;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 光线泄露效果 */
        .light-leak {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 255, 0, 0.2) 0%, rgba(0, 255, 0, 0) 70%);
            transform: rotate(45deg);
            z-index: 1;
            pointer-events: none;
            animation: light-leak-move 20s infinite alternate;
        }
        
        @keyframes light-leak-move {
            0% { transform: rotate(45deg) translate(-10%, -10%); }
            100% { transform: rotate(45deg) translate(10%, 10%); }
        }
        
        .light-leak-2 {
            position: absolute;
            bottom: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 200, 0, 0.15) 0%, rgba(0, 200, 0, 0) 70%);
            transform: rotate(-30deg);
            z-index: 1;
            pointer-events: none;
            animation: light-leak-move-2 25s infinite alternate;
        }
        
        @keyframes light-leak-move-2 {
            0% { transform: rotate(-30deg) translate(10%, -10%); }
            100% { transform: rotate(-30deg) translate(-10%, 10%); }
        }
        
        /* 划痕效果 */
        .scratch {
            position: absolute;
            background: linear-gradient(90deg, rgba(0, 255, 0, 0), rgba(0, 255, 0, 0.3), rgba(0, 255, 0, 0));
            height: 1px;
            width: 100%;
            z-index: 1;
            pointer-events: none;
        }
        .scratch-1 { top: 20%; transform: rotate(0.5deg); }
        .scratch-2 { top: 65%; transform: rotate(-0.7deg); }
        .scratch-3 { top: 40%; transform: rotate(0.3deg); width: 70%; left: 15%; }
        .scratch-4 { top: 30%; transform: rotate(-0.2deg); width: 60%; right: 10%; }
        
        /* 电影胶片装饰 */
        .film-strip {
            position: absolute;
            width: 100%;
            height: 40px;
            background: repeating-linear-gradient(90deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8) 10px, rgba(0, 255, 0, 0.3) 10px, rgba(0, 255, 0, 0.3) 20px);
            z-index: 1;
        }
        .film-strip-top { top: 0; }
        .film-strip-bottom { bottom: 0; }
        
        .film-hole {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(0, 255, 0, 0.5);
        }
        
        /* 封面页样式 */
        .content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px;
            flex-grow: 1;
            position: relative;
            z-index: 2;
            width: calc(100% - 40px);
            height: calc(100% - 40px);
            margin: 20px;
        }
        
        .logo-container {
            margin-bottom: 50px;
            position: relative;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .logo-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(0, 255, 0, 0.3) 0%, rgba(0, 255, 0, 0) 70%);
            border-radius: 50%;
            filter: blur(10px);
            animation: pulse 4s infinite alternate;
        }
        
        @keyframes pulse {
            0% { transform: scale(0.95); opacity: 0.7; }
            100% { transform: scale(1.05); opacity: 0.9; }
        }
        
        .logo-inner {
            width: 100px;
            height: 100px;
            background: rgba(20, 20, 20, 0.8);
            border: 3px solid rgba(0, 255, 0, 0.8);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(5px);
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
        
        .logo-inner i {
            font-size: 40px;
            color: #00ff00;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
        }
        
        .title-container {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            width: 100%;
            max-width: 800px;
        }
        
        .title {
            font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif;
            font-size: clamp(40px, 6.25vw, 80px);
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 5px;
            color: #00ff00;
            text-transform: uppercase;
            position: relative;
            text-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
            transform: skew(-5deg);
            white-space: nowrap;
        }
        
        .subtitle {
            font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif;
            font-size: clamp(20px, 2.5vw, 32px);
            font-weight: 300;
            color: #00cc00;
            letter-spacing: 8px;
            text-transform: uppercase;
            margin-top: 20px;
            text-shadow: 0 0 15px rgba(0, 204, 0, 0.5);
            white-space: nowrap;
        }
        
        .decorative-line {
            width: 200px;
            height: 3px;
            background: linear-gradient(90deg, rgba(0, 255, 0, 0), rgba(0, 255, 0, 0.8), rgba(0, 255, 0, 0));
            margin: 30px auto;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        .tagline {
            font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif;
            font-size: clamp(16px, 1.88vw, 24px);
            color: #ffffff;
            text-align: center;
            max-width: 600px;
            line-height: 1.6;
            margin-top: 30px;
            letter-spacing: 2px;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }
        
        /* 图标容器 */
        .icons-container {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
            width: 100%;
            max-width: 600px;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.3s ease;
            flex: 1;
        }
        
        .icon-item:hover {
            transform: translateY(-10px);
        }
        
        .icon-circle {
            width: 70px;
            height: 70px;
            background: rgba(20, 20, 20, 0.8);
            border: 2px solid rgba(0, 255, 0, 0.8);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .icon-circle i {
            font-size: 30px;
            color: #00ff00;
        }
        
        .icon-label {
            font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif;
            font-size: clamp(14px, 1.25vw, 18px);
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-align: center;
        }
        
        .footer {
            padding: 20px 70px;
            text-align: center;
            font-size: clamp(14px, 1.41vw, 18px);
            color: #00ff00;
            position: relative;
            z-index: 2;
            font-family: 'Roboto Condensed', sans-serif;
            letter-spacing: 3px;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        /* 导航控制 */
        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }
        
        .nav-btn {
            background: rgba(0, 255, 0, 0.2);
            border: 2px solid rgba(0, 255, 0, 0.5);
            color: #00ff00;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Roboto Condensed', sans-serif;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            backdrop-filter: blur(5px);
        }
        
        .nav-btn:hover {
            background: rgba(0, 255, 0, 0.3);
            border-color: rgba(0, 255, 0, 0.8);
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
        }
        
        .slide-indicator {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(0, 255, 0, 0.3);
            border: 1px solid rgba(0, 255, 0, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dot.active {
            background: rgba(0, 255, 0, 0.8);
            border-color: #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        /* 响应式调整 */
        @media (max-aspect-ratio: 1280/720) {
            .slide {
                width: 100vw;
                height: calc(100vw * 720 / 1280);
            }
        }
        
        @media (min-aspect-ratio: 1280/720) {
            .slide {
                width: calc(100vh * 1280 / 720);
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <!-- 第一页：封面 -->
        <div class="slide active" id="slide1">
            <div class="texture"></div>
            <div class="film-border"></div>
            <div class="film-strip film-strip-top"></div>
            <div class="film-strip film-strip-bottom"></div>
            
            <div class="grain"></div>
            <div class="light-leak"></div>
            <div class="light-leak-2"></div>
            <div class="scratch scratch-1"></div>
            <div class="scratch scratch-2"></div>
            <div class="scratch scratch-3"></div>
            <div class="scratch scratch-4"></div>
            
            <div class="content">
                <div class="logo-container">
                    <div class="logo-bg"></div>
                    <div class="logo">
                        <div class="logo-inner">
                            <i class="fas fa-leaf"></i>
                        </div>
            </div>
        </div>

                <div class="title-container">
                    <h1 class="title">兮禾文化</h1>
                    <h2 class="subtitle">商业计划书</h2>
        </div>

                <div class="decorative-line"></div>
                
                <p class="tagline">AI驱动的文化价值重构者</p>
                
                <div class="icons-container">
                    <div class="icon-item">
                        <div class="icon-circle">
                            <i class="fas fa-brain"></i>
            </div>
                        <div class="icon-label">智能</div>
            </div>
                    <div class="icon-item">
                        <div class="icon-circle">
                            <i class="fas fa-palette"></i>
            </div>
                        <div class="icon-label">创意</div>
                        </div>
                    <div class="icon-item">
                        <div class="icon-circle">
                            <i class="fas fa-leaf"></i>
                    </div>
                        <div class="icon-label">文化</div>
                        </div>
                    <div class="icon-item">
                        <div class="icon-circle">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="icon-label">增长</div>
                </div>
            </div>
        </div>

            <div class="footer">
                2025
            </div>
        </div>

        <!-- 第二页和第三页将通过JavaScript异步加载 -->
        </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
        </div>

    <!-- 幻灯片指示器 -->
    <div class="slide-indicator">
        <div class="dot active" onclick="currentSlide(1)"></div>
        <div class="dot" onclick="currentSlide(2)"></div>
        <div class="dot" onclick="currentSlide(3)"></div>
        </div>

    <script>
        let currentSlideIndex = 1;
        const totalSlides = 3;
        let slidesLoaded = [true, false, false]; // 追踪哪些幻灯片已加载

        // 动态生成胶片孔
        function generateFilmHoles(slide) {
            // 顶部胶片孔
            for (let i = 30; i <= 1230; i += 40) {
                const hole = document.createElement('div');
                hole.className = 'film-hole';
                hole.style.top = '16px';
                hole.style.left = i + 'px';
                slide.appendChild(hole);
            }
            // 底部胶片孔
            for (let i = 30; i <= 1230; i += 40) {
                const hole = document.createElement('div');
                hole.className = 'film-hole';
                hole.style.bottom = '16px';
                hole.style.left = i + 'px';
                slide.appendChild(hole);
            }
        }

        // 创建第二页：核心理念
        function createSlide2() {
            if (slidesLoaded[1]) return;
            
            const slide = document.createElement('div');
            slide.className = 'slide';
            slide.id = 'slide2';
            slide.innerHTML = `
                <div class="texture"></div>
                <div class="film-border"></div>
                <div class="film-strip film-strip-top"></div>
                <div class="film-strip film-strip-bottom"></div>
                <div class="grain"></div>
                <div class="light-leak"></div>
                <div class="light-leak-2"></div>
                <div class="scratch scratch-1"></div>
                <div class="scratch scratch-2"></div>
                <div class="scratch scratch-3"></div>
                <div class="scratch scratch-4"></div>
                
                <div style="display: flex; flex-direction: column; padding: 60px 70px; flex-grow: 1; position: relative; z-index: 2;">
                    <div style="margin-bottom: 50px;">
                        <h1 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 50px; font-weight: 700; margin-bottom: 16px; letter-spacing: 2px; color: rgba(0, 255, 100, 0.9); text-transform: uppercase; position: relative; text-shadow: 0 0 20px rgba(0, 255, 100, 0.3);">核心理念与技术理念</h1>
        </div>

                    <div style="display: flex; gap: 40px; flex-grow: 1;">
                        <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 30px; backdrop-filter: blur(5px); display: flex; flex-direction: column; position: relative; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                            <div style="display: flex; align-items: center; margin-bottom: 30px;">
                                <div style="display: flex; justify-content: center; align-items: center; width: 60px; height: 60px; border-radius: 50%; margin-right: 20px; flex-shrink: 0; position: relative;">
                                    <div style="width: 50px; height: 50px; background: rgba(10, 26, 10, 0.8); border: 2px solid rgba(0, 255, 100, 0.5); border-radius: 50%; display: flex; justify-content: center; align-items: center; position: relative; z-index: 2; backdrop-filter: blur(5px);">
                                        <i class="fas fa-yin-yang" style="font-size: 24px; color: rgba(0, 255, 100, 0.9);"></i>
            </div>
            </div>
                                <h2 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 32px; font-weight: 700; letter-spacing: 1px; color: rgba(0, 255, 100, 0.9); text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);">文心・商道</h2>
            </div>
                            <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: center;">
                                <div style="display: flex; align-items: flex-start; margin-bottom: 25px; position: relative;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.3);">
                                        <i class="fas fa-lightbulb" style="font-size: 18px; color: rgba(0, 255, 100, 0.9);"></i>
                        </div>
                                    <div style="flex-grow: 1;">
                                        <div style="font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif; font-size: 22px; font-weight: 600; margin-bottom: 8px; letter-spacing: 1px; color: rgba(0, 255, 100, 0.9);">文化价值挖掘</div>
                                        <div style="font-size: 16px; line-height: 1.5; color: rgba(255, 255, 255, 0.7);">深度挖掘传统文化内涵，提炼核心价值</div>
                    </div>
                        </div>
                                <div style="display: flex; align-items: flex-start; margin-bottom: 25px; position: relative;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.3);">
                                        <i class="fas fa-sync-alt" style="font-size: 18px; color: rgba(0, 255, 100, 0.9);"></i>
                        </div>
                                    <div style="flex-grow: 1;">
                                        <div style="font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif; font-size: 22px; font-weight: 600; margin-bottom: 8px; letter-spacing: 1px; color: rgba(0, 255, 100, 0.9);">现代语境转化</div>
                                        <div style="font-size: 16px; line-height: 1.5; color: rgba(255, 255, 255, 0.7);">将传统文化元素转化为现代表达</div>
                        </div>
                    </div>
                                <div style="display: flex; align-items: flex-start; margin-bottom: 0; position: relative;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.3);">
                                        <i class="fas fa-handshake" style="font-size: 18px; color: rgba(0, 255, 100, 0.9);"></i>
                </div>
                                    <div style="flex-grow: 1;">
                                        <div style="font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif; font-size: 22px; font-weight: 600; margin-bottom: 8px; letter-spacing: 1px; color: rgba(0, 255, 100, 0.9);">商业价值创造</div>
                                        <div style="font-size: 16px; line-height: 1.5; color: rgba(255, 255, 255, 0.7);">实现文化价值与商业价值的双赢</div>
                        </div>
                </div>
            </div>
        </div>

                        <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 30px; backdrop-filter: blur(5px); display: flex; flex-direction: column; position: relative; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                            <div style="display: flex; align-items: center; margin-bottom: 30px;">
                                <div style="display: flex; justify-content: center; align-items: center; width: 60px; height: 60px; border-radius: 50%; margin-right: 20px; flex-shrink: 0; position: relative;">
                                    <div style="width: 50px; height: 50px; background: rgba(10, 26, 10, 0.8); border: 2px solid rgba(0, 255, 100, 0.5); border-radius: 50%; display: flex; justify-content: center; align-items: center; position: relative; z-index: 2; backdrop-filter: blur(5px);">
                                        <i class="fas fa-microchip" style="font-size: 24px; color: rgba(0, 200, 80, 0.9);"></i>
            </div>
            </div>
                                <h2 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 32px; font-weight: 700; letter-spacing: 1px; color: rgba(0, 200, 80, 0.9); text-shadow: 0 0 15px rgba(0, 200, 80, 0.3);">AI为笔，文化为墨</h2>
            </div>
                            <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: center;">
                                <div style="display: flex; align-items: flex-start; margin-bottom: 25px; position: relative;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 200, 80, 0.3);">
                                        <i class="fas fa-brain" style="font-size: 18px; color: rgba(0, 200, 80, 0.9);"></i>
                        </div>
                                    <div style="flex-grow: 1;">
                                        <div style="font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif; font-size: 22px; font-weight: 600; margin-bottom: 8px; letter-spacing: 1px; color: rgba(0, 200, 80, 0.9);">AI赋能创作</div>
                                        <div style="font-size: 16px; line-height: 1.5; color: rgba(255, 255, 255, 0.7);">利用AI技术提升创作效率与质量</div>
                        </div>
                        </div>
                                <div style="display: flex; align-items: flex-start; margin-bottom: 25px; position: relative;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 200, 80, 0.3);">
                                        <i class="fas fa-database" style="font-size: 18px; color: rgba(0, 200, 80, 0.9);"></i>
                        </div>
                                    <div style="flex-grow: 1;">
                                        <div style="font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif; font-size: 22px; font-weight: 600; margin-bottom: 8px; letter-spacing: 1px; color: rgba(0, 200, 80, 0.9);">文化数据驱动</div>
                                        <div style="font-size: 16px; line-height: 1.5; color: rgba(255, 255, 255, 0.7);">构建文化基因数据库，支撑智能创作</div>
                    </div>
                    </div>
                                <div style="display: flex; align-items: flex-start; margin-bottom: 0; position: relative;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 200, 80, 0.3);">
                                        <i class="fas fa-shield-alt" style="font-size: 18px; color: rgba(0, 200, 80, 0.9);"></i>
                </div>
                                    <div style="flex-grow: 1;">
                                        <div style="font-family: 'Roboto Condensed', 'Noto Sans SC', sans-serif; font-size: 22px; font-weight: 600; margin-bottom: 8px; letter-spacing: 1px; color: rgba(0, 200, 80, 0.9);">安全与合规</div>
                                        <div style="font-size: 16px; line-height: 1.5; color: rgba(255, 255, 255, 0.7);">确保AI创作符合文化安全要求</div>
                </div>
            </div>
        </div>
            </div>
            </div>
        </div>

                <div class="footer">
                    兮禾文化 · 创意与科技的融合
            </div>
            `;
            
            generateFilmHoles(slide);
            document.querySelector('.slideshow-container').appendChild(slide);
            slidesLoaded[1] = true;
        }

        // 创建第三页：时代机遇
        function createSlide3() {
            if (slidesLoaded[2]) return;
            
            const slide = document.createElement('div');
            slide.className = 'slide';
            slide.id = 'slide3';
            slide.innerHTML = `
                <div class="texture"></div>
                <div class="film-border"></div>
                <div class="film-strip film-strip-top"></div>
                <div class="film-strip film-strip-bottom"></div>
                <div class="grain"></div>
                <div class="light-leak"></div>
                <div class="light-leak-2"></div>
                <div class="scratch scratch-1"></div>
                <div class="scratch scratch-2"></div>
                <div class="scratch scratch-3"></div>
                <div class="scratch scratch-4"></div>
                
                <div style="display: flex; flex-direction: column; padding: 60px 70px; flex-grow: 1; position: relative; z-index: 2;">
                    <div style="margin-bottom: 40px;">
                        <h1 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 50px; font-weight: 700; margin-bottom: 16px; letter-spacing: 2px; color: rgba(0, 255, 100, 0.9); text-transform: uppercase; position: relative; text-shadow: 0 0 20px rgba(0, 255, 100, 0.3);">时代机遇：内容产业的范式革命</h1>
        </div>

                    <div style="display: flex; flex-direction: column; gap: 30px; flex-grow: 1;">
                        <div style="display: flex; justify-content: space-between; gap: 20px; margin-bottom: 30px;">
                            <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 20px; text-align: center; position: relative; transition: transform 0.3s ease, box-shadow 0.3s ease; overflow: hidden;">
                                <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 30px; height: 30px; background: rgba(0, 255, 100, 0.8); border-radius: 50%; display: flex; justify-content: center; align-items: center; font-weight: 700; font-size: 16px; font-family: 'Bebas Neue', sans-serif; z-index: 2; color: rgba(10, 26, 10, 0.9);">1</div>
                                <div style="font-size: 36px; margin-bottom: 15px; color: rgba(0, 255, 100, 0.9); text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);">
                                    <i class="fas fa-chart-line"></i>
            </div>
                                <h3 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 24px; font-weight: 600; margin: 10px 0; color: rgba(0, 255, 100, 0.9); text-transform: uppercase; letter-spacing: 1px;">流量时代</h3>
                                <p style="font-size: 16px; color: rgba(255, 255, 255, 0.7); line-height: 1.5;">以平台为中心，追求流量变现</p>
            </div>
                            <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 20px; text-align: center; position: relative; transition: transform 0.3s ease, box-shadow 0.3s ease; overflow: hidden;">
                                <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 30px; height: 30px; background: rgba(0, 255, 100, 0.8); border-radius: 50%; display: flex; justify-content: center; align-items: center; font-weight: 700; font-size: 16px; font-family: 'Bebas Neue', sans-serif; z-index: 2; color: rgba(10, 26, 10, 0.9);">2</div>
                                <div style="font-size: 36px; margin-bottom: 15px; color: rgba(0, 255, 100, 0.9); text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);">
                                    <i class="fas fa-pen-fancy"></i>
            </div>
                                <h3 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 24px; font-weight: 600; margin: 10px 0; color: rgba(0, 255, 100, 0.9); text-transform: uppercase; letter-spacing: 1px;">内容时代</h3>
                                <p style="font-size: 16px; color: rgba(255, 255, 255, 0.7); line-height: 1.5;">以创作者为中心，追求内容质量</p>
                        </div>
                            <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 20px; text-align: center; position: relative; transition: transform 0.3s ease, box-shadow 0.3s ease; overflow: hidden;">
                                <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 30px; height: 30px; background: rgba(0, 255, 100, 0.8); border-radius: 50%; display: flex; justify-content: center; align-items: center; font-weight: 700; font-size: 16px; font-family: 'Bebas Neue', sans-serif; z-index: 2; color: rgba(10, 26, 10, 0.9);">3</div>
                                <div style="font-size: 36px; margin-bottom: 15px; color: rgba(0, 255, 100, 0.9); text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);">
                                    <i class="fas fa-copyright"></i>
                    </div>
                                <h3 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 24px; font-weight: 600; margin: 10px 0; color: rgba(0, 255, 100, 0.9); text-transform: uppercase; letter-spacing: 1px;">IP时代</h3>
                                <p style="font-size: 16px; color: rgba(255, 255, 255, 0.7); line-height: 1.5;">以IP为中心，追求品牌价值</p>
                        </div>
                            <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 20px; text-align: center; position: relative; transition: transform 0.3s ease, box-shadow 0.3s ease; overflow: hidden;">
                                <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 30px; height: 30px; background: rgba(0, 255, 100, 0.8); border-radius: 50%; display: flex; justify-content: center; align-items: center; font-weight: 700; font-size: 16px; font-family: 'Bebas Neue', sans-serif; z-index: 2; color: rgba(10, 26, 10, 0.9);">4</div>
                                <div style="font-size: 36px; margin-bottom: 15px; color: rgba(0, 255, 100, 0.9); text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);">
                                    <i class="fas fa-brain"></i>
                        </div>
                                <h3 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 24px; font-weight: 600; margin: 10px 0; color: rgba(0, 255, 100, 0.9); text-transform: uppercase; letter-spacing: 1px;">思想时代</h3>
                                <p style="font-size: 16px; color: rgba(255, 255, 255, 0.7); line-height: 1.5;">以思想为中心，追求文化影响力</p>
            </div>
        </div>

                        <div style="display: flex; gap: 30px; flex-grow: 1;">
                            <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 25px; display: flex; flex-direction: column; position: relative; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                                <div style="position: absolute; top: 0; left: 0; width: 6px; height: 100%; z-index: -1; background: linear-gradient(to bottom, rgba(255, 50, 50, 0.7), rgba(255, 50, 50, 0.3));"></div>
                                <div style="display: flex; align-items: center; margin-bottom: 25px;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 50px; height: 50px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; position: relative;">
                                        <div style="width: 40px; height: 40px; background: rgba(10, 26, 10, 0.8); border: 2px solid rgba(255, 50, 50, 0.7); border-radius: 50%; display: flex; justify-content: center; align-items: center; position: relative; z-index: 2; backdrop-filter: blur(5px);">
                                            <i class="fas fa-exclamation-triangle" style="font-size: 20px; color: rgba(255, 50, 50, 0.9);"></i>
            </div>
            </div>
                                    <h2 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 28px; font-weight: 600; letter-spacing: 1px; color: rgba(255, 50, 50, 0.9); text-shadow: 0 0 15px rgba(255, 50, 50, 0.3);">行业痛点</h2>
            </div>
                                <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: center;">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 20px; position: relative;">
                                        <i class="fas fa-battery-quarter" style="font-size: 18px; margin-right: 12px; flex-shrink: 0; margin-top: 3px; color: rgba(255, 50, 50, 0.9);"></i>
                                        <p style="font-size: 18px; line-height: 1.5; color: rgba(255, 255, 255, 0.8);">
                                            <span style="font-weight: 500; color: rgba(255, 50, 50, 0.9);">"人肉干电池"</span>模式：创意人才过度消耗，效率低下
                                        </p>
                            </div>
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 20px; position: relative;">
                                        <i class="fas fa-random" style="font-size: 18px; margin-right: 12px; flex-shrink: 0; margin-top: 3px; color: rgba(255, 50, 50, 0.9);"></i>
                                        <p style="font-size: 18px; line-height: 1.5; color: rgba(255, 255, 255, 0.8);">
                                            <span style="font-weight: 500; color: rgba(255, 50, 50, 0.9);">碎片化创作</span>：缺乏系统性，难以形成文化IP
                                        </p>
                            </div>
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 0; position: relative;">
                                        <i class="fas fa-lock" style="font-size: 18px; margin-right: 12px; flex-shrink: 0; margin-top: 3px; color: rgba(255, 50, 50, 0.9);"></i>
                                        <p style="font-size: 18px; line-height: 1.5; color: rgba(255, 255, 255, 0.8);">
                                            <span style="font-weight: 500; color: rgba(255, 50, 50, 0.9);">文化安全风险</span>：AI生成内容缺乏有效监管
                                        </p>
                        </div>
                            </div>
                            </div>
                            
                            <div style="flex: 1; background: rgba(10, 26, 10, 0.6); border: 1px solid rgba(0, 255, 100, 0.2); border-radius: 4px; padding: 25px; display: flex; flex-direction: column; position: relative; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                                <div style="position: absolute; top: 0; left: 0; width: 6px; height: 100%; z-index: -1; background: linear-gradient(to bottom, rgba(0, 255, 100, 0.7), rgba(0, 255, 100, 0.3));"></div>
                                <div style="display: flex; align-items: center; margin-bottom: 25px;">
                                    <div style="display: flex; justify-content: center; align-items: center; width: 50px; height: 50px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; position: relative;">
                                        <div style="width: 40px; height: 40px; background: rgba(10, 26, 10, 0.8); border: 2px solid rgba(0, 255, 100, 0.7); border-radius: 50%; display: flex; justify-content: center; align-items: center; position: relative; z-index: 2; backdrop-filter: blur(5px);">
                                            <i class="fas fa-lightbulb" style="font-size: 20px; color: rgba(0, 255, 100, 0.9);"></i>
                        </div>
                            </div>
                                    <h2 style="font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif; font-size: 28px; font-weight: 600; letter-spacing: 1px; color: rgba(0, 255, 100, 0.9); text-shadow: 0 0 15px rgba(0, 255, 100, 0.3);">我们的机遇</h2>
                            </div>
                                <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: center;">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 20px; position: relative;">
                                        <i class="fas fa-robot" style="font-size: 18px; margin-right: 12px; flex-shrink: 0; margin-top: 3px; color: rgba(0, 255, 100, 0.9);"></i>
                                        <p style="font-size: 18px; line-height: 1.5; color: rgba(255, 255, 255, 0.8);">
                                            <span style="font-weight: 500; color: rgba(0, 255, 100, 0.9);">AI赋能创作</span>：提升效率，释放创意人才潜能
                                        </p>
                        </div>
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 20px; position: relative;">
                                        <i class="fas fa-project-diagram" style="font-size: 18px; margin-right: 12px; flex-shrink: 0; margin-top: 3px; color: rgba(0, 255, 100, 0.9);"></i>
                                        <p style="font-size: 18px; line-height: 1.5; color: rgba(255, 255, 255, 0.8);">
                                            <span style="font-weight: 500; color: rgba(0, 255, 100, 0.9);">思想驱动</span>：从碎片化内容到系统性文化IP
                                        </p>
                    </div>
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 0; position: relative;">
                                        <i class="fas fa-shield-alt" style="font-size: 18px; margin-right: 12px; flex-shrink: 0; margin-top: 3px; color: rgba(0, 255, 100, 0.9);"></i>
                                        <p style="font-size: 18px; line-height: 1.5; color: rgba(255, 255, 255, 0.8);">
                                            <span style="font-weight: 500; color: rgba(0, 255, 100, 0.9);">文化安全机制</span>：AI+专家双重审核，确保合规
                                        </p>
                            </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>

                <div class="footer">
                    兮禾文化 · 创意与科技的融合
            </div>
            `;
            
            generateFilmHoles(slide);
            document.querySelector('.slideshow-container').appendChild(slide);
            slidesLoaded[2] = true;
        }

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');
            const dots = document.querySelectorAll('.dot');
            
            if (n > totalSlides) currentSlideIndex = 1;
            if (n < 1) currentSlideIndex = totalSlides;
            
            // 加载需要的幻灯片
            if (currentSlideIndex === 2 && !slidesLoaded[1]) {
                createSlide2();
            } else if (currentSlideIndex === 3 && !slidesLoaded[2]) {
                createSlide3();
            }
            
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));
            
            const currentSlide = document.getElementById(`slide${currentSlideIndex}`);
            if (currentSlide) {
                currentSlide.classList.add('active');
            }
            
            if (dots[currentSlideIndex - 1]) {
                dots[currentSlideIndex - 1].classList.add('active');
            }
            }

            function nextSlide() {
            currentSlideIndex++;
            showSlide(currentSlideIndex);
        }

        function previousSlide() {
            currentSlideIndex--;
            showSlide(currentSlideIndex);
        }

        function currentSlide(n) {
            currentSlideIndex = n;
            showSlide(currentSlideIndex);
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowRight' || event.key === ' ') {
                nextSlide();
            } else if (event.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // 初始化
        window.addEventListener('DOMContentLoaded', function() {
            generateFilmHoles(document.getElementById('slide1'));
            showSlide(currentSlideIndex);
        });
    </script>
</body>
</html>
