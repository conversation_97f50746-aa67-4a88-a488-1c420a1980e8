<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>时代机遇 - 兮禾文化商业计划书</title>
<link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Condensed:wght@300;400;700&family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    body {
        font-family: 'Noto Sans SC', sans-serif;
        overflow: hidden;
        background: #000;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100vw;
    }

    .slide {
        position: relative;
        width: 100%;
        height: 100%;
        max-width: 1280px;
        max-height: 720px;
        aspect-ratio: 16/9;
        background: #0d0d0d;
        color: #ffffff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 40px 60px;
    }
    
    /* 复用视觉特效 */
    .texture, .grain, .light-leak {
        position: absolute; z-index: 1; pointer-events: none; width: 100%; height: 100%; top: 0; left: 0;
    }
    .texture {
        background-image: linear-gradient(rgba(0, 255, 0, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 255, 0, 0.03) 1px, transparent 1px);
        background-size: 40px 40px;
    }
    .grain {
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iMC45IiBudW1PY3RhdmVzPSIxIi8+PC9maWx0ZXI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIwLjA1Ii8+PC9zdmc+');
        opacity: 0.8;
    }
    .light-leak {
        width: 200%; height: 200%;
        background: radial-gradient(circle, rgba(0, 255, 0, 0.08) 0%, rgba(0, 255, 0, 0) 60%);
        top: -50%; right: -50%; animation: light-leak-anim 25s infinite alternate;
    }
    @keyframes light-leak-anim {
        from { transform: translate(10%, -10%); }
        to { transform: translate(-10%, 10%); }
    }

    .content {
        position: relative;
        z-index: 2;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .header {
        text-align: left;
        margin-bottom: 40px;
    }
    .header h1 {
        font-family: 'Bebas Neue', 'Noto Sans SC', sans-serif;
        font-size: clamp(32px, 4vw, 50px);
        color: #00ff00;
        letter-spacing: 3px;
        text-transform: uppercase;
        text-shadow: 0 0 15px rgba(0, 255, 0, 0.4);
    }
    .header h2 {
        font-family: 'Roboto Condensed', sans-serif;
        font-size: clamp(16px, 1.8vw, 24px);
        color: #cccccc;
        font-weight: 300;
        letter-spacing: 2px;
        margin-top: 10px;
    }

    .timeline-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }
    
    .timeline-line {
        position: absolute;
        left: 5%;
        right: 5%;
        top: 50%;
        height: 2px;
        background: linear-gradient(90deg, rgba(0, 255, 0, 0), rgba(0, 255, 0, 0.4), rgba(0, 255, 0, 0));
        transform: translateY(-50%);
    }

    .timeline-eras {
        display: flex;
        justify-content: space-between;
        width: 100%;
        position: relative;
    }

    .era {
        width: 22%;
        text-align: center;
        position: relative;
    }

    .era-content {
        background: rgba(10, 26, 10, 0.7);
        border: 1px solid rgba(0, 255, 0, 0.2);
        border-radius: 8px;
        padding: 20px 15px;
        backdrop-filter: blur(5px);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .era:hover .era-content {
        transform: translateY(-10px);
        box-shadow: 0 10px 30px rgba(0, 255, 0, 0.2);
    }

    .era-title {
        font-family: 'Bebas Neue', sans-serif;
        font-size: clamp(20px, 2.2vw, 28px);
        letter-spacing: 1px;
        margin-bottom: 10px;
    }
    .era-description {
        font-size: clamp(12px, 1.2vw, 14px);
        line-height: 1.6;
        color: #b0b0b0;
        min-height: 80px;
    }
    .era-keyword {
        display: inline-block;
        font-family: 'Roboto Condensed', sans-serif;
        font-size: clamp(14px, 1.5vw, 16px);
        font-weight: 700;
        padding: 6px 15px;
        border-radius: 20px;
        margin-top: 15px;
        text-transform: uppercase;
    }
    
    .era-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #0d0d0d;
        border: 3px solid;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        transition: transform 0.3s ease;
    }
    .era:hover .era-dot {
        transform: translate(-50%, -50%) scale(1.5);
    }
    
    /* Era specific styling */
    .era-1 .era-title, .era-1 .era-dot { color: #888; border-color: #888; }
    .era-1 .era-keyword { background-color: #444; color: #ccc; }
    .era-2 .era-title, .era-2 .era-dot { color: #ff4d4f; border-color: #ff4d4f; }
    .era-2 .era-keyword { background-color: #ff4d4f; color: #fff; }
    .era-3 .era-title, .era-3 .era-dot { color: #ccc; border-color: #ccc; }
    .era-3 .era-keyword { background-color: #555; color: #ddd; }
    .era-4 .era-title, .era-4 .era-dot { color: #00ff00; border-color: #00ff00; }
    .era-4 .era-keyword { background-color: #00ff00; color: #000; }
    .era-4 .era-content { border-color: rgba(0, 255, 0, 0.5); }


    .footer {
        position: absolute;
        bottom: 20px;
        left: 60px;
        font-family: 'Roboto Condensed', sans-serif;
        font-size: 14px;
        color: rgba(0, 255, 0, 0.5);
        letter-spacing: 2px;
        z-index: 2;
    }
    
    /* Responsive */
    @media (max-aspect-ratio: 16/9) { .slide { width: 100vw; height: calc(100vw * 9 / 16); } }
    @media (min-aspect-ratio: 16/9) { .slide { width: calc(100vh * 16 / 9); height: 100vh; } }

</style>
</head>
<body>
    <div class="slide">
        <!-- Background FX -->
        <div class="texture"></div>
        <div class="grain"></div>
        <div class="light-leak"></div>
        
        <div class="content">
            <div class="header">
                <h1>ERA ANALYSIS: THE PARADIGM SHIFT</h1>
                <h2>我们看到的行业真相：内容产业的范式革命</h2>
            </div>
            
            <div class="timeline-container">
                <div class="timeline-line"></div>
                <div class="timeline-eras">
                    <div class="era era-1">
                        <div class="era-content">
                            <h3 class="era-title">1.0 PORTAL ERA</h3>
                            <p class="era-description">传统媒体权威被削弱，网络平台开始分割话语权，个人作坊式创作者出现。</p>
                            <span class="era-keyword">权力分散</span>
                        </div>
                        <div class="era-dot"></div>
                    </div>
                    <div class="era era-2">
                         <div class="era-content">
                            <h3 class="era-title">2.0 SOCIAL MEDIA ERA</h3>
                            <p class="era-description">MCN机构涌现，流量竞争白热化，行业陷入低效、同质化的内卷。</p>
                            <span class="era-keyword">人肉干电池</span>
                        </div>
                        <div class="era-dot"></div>
                    </div>
                    <div class="era era-3">
                         <div class="era-content">
                            <h3 class="era-title">3.0 AI-ASSISTED ERA</h3>
                            <p class="era-description">AI被用于提升分发效率和辅助创作，但核心生产模式并未改变。</p>
                            <span class="era-keyword">效率提升</span>
                        </div>
                        <div class="era-dot"></div>
                    </div>
                    <div class="era era-4">
                         <div class="era-content">
                            <h3 class="era-title">4.0 MIND-DRIVEN ERA</h3>
                            <p class="era-description">AI Agent解放生产力，竞争核心回归思想、创意与文化深度。</p>
                            <span class="era-keyword">价值回归</span>
                        </div>
                        <div class="era-dot"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            兮禾文化 · 行业洞察
        </div>
    </div>
</body>
</html>